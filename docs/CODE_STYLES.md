# Rust Code Style Guidelines

This document provides comprehensive code style guidelines for AI assistants working on the <PERSON><PERSON><PERSON><PERSON> project. These guidelines are derived from TypeScript/JavaScript best practices and adapted for Rust development.

## Project Overview

The **Shreder** project is a Rust application focused on high-performance data processing. This document ensures consistent, maintainable, and idiomatic Rust code style across the entire codebase.

## Configuration Files

This project uses the following configuration files for automated code style enforcement:

- **`.rustfmt.toml`** - Rust code formatting configuration
- **`clippy.toml`** - Clippy linting configuration  
- **`Cargo.toml`** - Project dependencies and metadata

## Rustfmt Configuration

The `.rustfmt.toml` file contains the following key settings:

```toml
edition = "2024"
hard_tabs = false
tab_spaces = 4
max_width = 120
newline_style = "Unix"

use_small_heuristics = "Default"

fn_params_layout = "Tall"
fn_call_width = 60

reorder_imports = true
reorder_modules = true

match_block_trailing_comma = false
match_arm_leading_pipes = "Never"

short_array_element_width_threshold = 10
chain_width = 120
single_line_if_else_max_width = 0

remove_nested_parens = true
use_field_init_shorthand = true
force_explicit_abi = true
```

### Key Formatting Rules

- **4 spaces indentation** (consistent with TypeScript projects)
- **120 character line width**
- **Unix line endings**
- **Method chains up to 120 characters on single line**
- **If-else statements always use braces and newlines**
- **Automatic import reordering**

## Clippy Configuration

The `clippy.toml` file contains the following key settings:

```toml
cognitive-complexity-threshold = 30
too-many-lines-threshold = 30
too-many-arguments-threshold = 7
type-complexity-threshold = 250

enum-variant-name-threshold = 3

trivial-copy-size-limit = 128

array-size-threshold = 512000
enum-variant-size-threshold = 200

large-error-threshold = 128

unreadable-literal-lint-fractions = true

allow-expect-in-tests = true
allow-unwrap-in-tests = true

verbose-bit-mask-threshold = 1

avoid-breaking-exported-api = true
check-private-items = false
```

### Key Linting Rules

- **Maximum 30 lines per function**
- **Maximum 7 function arguments**
- **Cognitive complexity threshold of 30**
- **Allow expect/unwrap in tests**
- **Performance and error handling thresholds**

## Formatting Rules

### General Formatting

- Use **4 spaces** for indentation
- Maximum line length of **120 characters**
- Use Unix line endings (LF)
- Remove trailing whitespace
- Files must end with a newline

### Code Structure

- Use **1TBS brace style** (one true brace style)
- Always use braces for control structures, even single statements
- Place opening brace on the same line as the declaration

```rust
// Correct
if condition {
    do_something();
}

// Incorrect
if condition
{
    do_something();
}
```

### Method Chaining

- Method chains up to 120 characters stay on single line
- For longer chains with text strings, use `#[rustfmt::skip]`

```rust
// Short chains stay on one line
let result = data.iter().map(|x| x * 2).collect();

// Long chains with text use rustfmt::skip
#[rustfmt::skip]
fn process_long_message(input: &str) -> String {
    input.trim().to_lowercase().replace("very long error message that exceeds 120 characters", "short").to_string()
}
```

### Control Flow

- If-else statements always use braces and newlines
- No single-line if-else expressions
- Prefer early returns

```rust
// Correct - always multi-line with braces
if user.is_active {
    "User is active".to_string()
} else {
    "User is inactive".to_string()
}

// Incorrect - single line
if user.is_active { "active" } else { "inactive" }
```

## Naming Conventions

### Variables and Functions

- Use **snake_case** for variables, functions, and modules
- Use descriptive names that clearly indicate purpose
- Prefix unused parameters with underscore: `_unused_param`

```rust
// Correct
let user_count = 10;
fn calculate_total_price() -> f64 { }
fn process_data(_unused_input: &str) { }

// Incorrect
let userCount = 10;
fn calculateTotalPrice() -> f64 { }
```

### Types and Constants

- Use **PascalCase** for types, structs, enums, and traits
- Use **SCREAMING_SNAKE_CASE** for constants and static variables

```rust
// Correct
struct UserAccount { }
enum MessageType { }
trait DataProcessor { }
const MAX_RETRY_COUNT: u32 = 3;

// Incorrect
struct user_account { }
enum messageType { }
const max_retry_count: u32 = 3;
```

### Files and Modules

- Use **snake_case** for file names: `user_service.rs`, `database_config.rs`
- Module names should match file names
- Use descriptive names that indicate module purpose

## Import Organization

Group imports in the following order with blank lines between groups:

1. **Standard library** (`std::`, `core::`)
2. **External crates** (alphabetical order)
3. **Internal modules** (relative imports)

```rust
// Standard library
use std::collections::HashMap;
use std::fs::File;

// External crates
use serde::{Deserialize, Serialize};
use tokio::time::Duration;

// Internal modules
use crate::config::AppConfig;
use crate::services::UserService;
```

## Function and Method Formatting

### Function Organization

- Keep functions focused on single responsibility
- **Maximum 30 lines per function** (enforced by clippy)
- **Maximum 7 parameters per function** (enforced by clippy)
- **Maximum cognitive complexity of 30** (enforced by clippy)

```rust
// Good: Single responsibility, clear purpose
fn calculate_user_score(user: &User) -> Result<u32, ScoreError> {
    validate_user(user)?;
    let base_score = compute_base_score(user);
    let bonus = calculate_bonus(user);
    Ok(base_score + bonus)
}
```

### Function Signatures

- Use "Tall" layout for function parameters
- Keep function call width under 60 characters
- Omit explicit return types for simple cases

```rust
// Good: Tall parameter layout
impl UserService {
    pub fn create_user(
        &mut self,
        email: String,
        username: String,
        display_name: Option<String>,
    ) -> Result<User, UserError> {
        // Implementation
    }
}
```

## Blank Line Management

Always add blank lines before and after:

- Function definitions
- Struct and enum definitions
- Impl blocks
- Module declarations
- Major code blocks

```rust
// Good: Proper blank line usage
fn process_user(user: &User) -> Result<ProcessedUser, UserError> {
    validate_user(user)?;

    let processed_data = transform_user_data(user);

    save_to_database(&processed_data)?;

    Ok(processed_data)
}

struct UserAccount {
    id: u64,
    email: String,
}

impl UserAccount {
    fn new(id: u64, email: String) -> Self {
        Self { id, email }
    }
}
```

## Struct and Enum Organization

- Group fields logically in structs
- Use consistent field ordering: required fields first, optional fields last
- Use trailing commas in multiline definitions
- No field alignment

```rust
// Good: Well-organized struct
#[derive(Debug, Clone)]
pub struct UserProfile {
    // Required identification
    pub id: u64,
    pub email: String,
    pub username: String,

    // Profile information
    pub display_name: Option<String>,
    pub bio: Option<String>,
    pub avatar_url: Option<String>,

    // Metadata
    pub is_active: bool,
    pub created_at: u64,
    pub updated_at: u64,
}
```

## Pattern Matching

- Use exhaustive pattern matching when possible
- Prefer `if let` for simple single-pattern matches
- Use `match` for complex pattern matching
- No trailing commas in match blocks
- Never use leading pipes in match arms

```rust
// Good: Exhaustive pattern matching
fn handle_response(response: ApiResponse) -> Result<Data, ApiError> {
    match response {
        ApiResponse::Success(data) => Ok(data),
        ApiResponse::NotFound => Err(ApiError::ResourceNotFound),
        ApiResponse::Unauthorized => Err(ApiError::AuthenticationFailed),
        ApiResponse::ServerError(msg) => Err(ApiError::ServerError(msg)),
    }
}

// Good: Simple pattern with if let
fn extract_user_id(token: &str) -> Option<u64> {
    if let Some(claims) = decode_token(token) {
        claims.user_id
    } else {
        None
    }
}
```

## Collection and Iterator Patterns

- Prefer iterators over index-based loops
- Use iterator combinators for data transformation
- Collect only when necessary
- Use appropriate collection types

```rust
// Good: Efficient iterator usage
fn process_active_users(users: &[User]) -> Vec<ProcessedUser> {
    users
        .iter()
        .filter(|user| user.is_active)
        .map(|user| ProcessedUser::from(user))
        .collect()
}

fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {
    users.iter().find(|user| user.email == email)
}
```

## String Handling

- Use `&str` for string slices and borrowed strings
- Use `String` for owned strings that need to be modified
- Prefer `format!` over string concatenation
- Use field init shorthand when possible

```rust
// Good: Appropriate string types
fn validate_email(email: &str) -> Result<(), ValidationError> {
    if email.is_empty() || !email.contains('@') {
        return Err(ValidationError::InvalidFormat);
    }
    Ok(())
}

fn format_user_display(user: &User) -> String {
    format!("{} <{}>", user.name, user.email)
}
```

## Trait Implementation

- Implement common traits when appropriate
- Use `#[derive]` for automatic implementations when possible
- Implement traits manually only when custom behavior is needed

```rust
// Good: Appropriate trait derivations
#[derive(Debug, Clone, PartialEq, Eq, Hash, Copy)]
pub struct UserId(u64);

#[derive(Debug, Clone)]
pub struct User {
    id: UserId,
    email: String,
    created_at: u64,
}

// Good: Custom trait implementation when needed
impl Display for User {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "User({}): {}", self.id.0, self.email)
    }
}
```

## Code Quality Rules

### Implementation Scope

- Implement only what is explicitly requested
- Avoid adding features beyond requirements
- Ask before implementing related improvements
- Focus on minimal viable implementation

### Default Exclusions

Unless explicitly requested, do not add:

- Extensive input validation beyond basic needs
- Comprehensive error handling for edge cases
- Example implementations or demo code
- Complex optimization without profiling data

### Code Cleanliness

- Never generate comments in source code by default
- Use self-documenting code through clear naming
- Keep functions focused on single responsibility
- Maintain consistent code organization across modules
- Eliminate all unused code: imports, variables, functions

## Tools and Commands

### Essential Commands

- `cargo fmt` - Format code according to .rustfmt.toml
- `cargo clippy` - Run linter with clippy.toml configuration
- `cargo test` - Run all tests
- `cargo check` - Fast compilation check
- `cargo build --release` - Optimized build

### Usage Examples

```bash
# Format all code
cargo fmt

# Check formatting without changes
cargo fmt --check

# Run clippy with strict warnings
cargo clippy -- -D warnings

# Run all quality checks
cargo fmt && cargo clippy && cargo test
```

This document ensures consistent, high-quality Rust code style that follows established patterns from TypeScript/JavaScript projects while embracing Rust's unique features and idioms.