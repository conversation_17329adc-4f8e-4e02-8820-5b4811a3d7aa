# PLAN.md - Shreder Development Roadmap

## Development Philosophy

### Core Principles

1. **Pre-Implementation Consultation**: Phải consultation với user trước khi implement bất kỳ tính năng nào
2. **User-Driven Development**: User approval required cho cả consultation và testing phases
3. **Incremental Dependency Management**: Chỉ cài dependency khi thực sự cần thiết
4. **User-Driven Library Selection**: AI hỏi user về lựa chọn thư viện trước khi cài đặt
5. **Module Independence**: Thiết kế modules để hạn chế dependencies lẫn nhau tối đa
6. **Implement First, Declare Later**: Chỉ update mod.rs khi module đã implement và test xong
7. **Single Responsibility**: Mỗi module focus vào một nhiệm vụ cụ thể
8. **Zero Comments Policy**: Tuyệt đối không viết comments trong source code
9. **Post-Implementation Testing**: Mỗi module phải được test và verify trước khi consider complete

### Quality Gates

-   **Consultation → Implementation → Testing → Integration** cycle cho mỗi module
-   Không proceed to next module cho đến khi current module hoàn toàn stable
-   User approval required ở cả consultation phase và testing completion phase

## Pre-Implementation Consultation Process

### Consultation Checklist (Required before any implementation)

1. **Purpose và Requirements Clarification**

    - Hỏi rõ về mục đích cụ thể của tính năng/module
    - Xác định các yêu cầu functional và non-functional
    - Clarify expected behavior và edge cases

2. **Feature Specification**

    - Xác nhận các features mong muốn chi tiết
    - Define acceptance criteria rõ ràng
    - Identify potential limitations hoặc constraints

3. **Implementation Strategy Discussion**

    - Thảo luận về approach user muốn sử dụng
    - Consider alternative implementation methods
    - Evaluate pros/cons của different approaches

4. **Library Selection Consultation**

    - Đề xuất library choices với rationale
    - Explain benefits/drawbacks của từng option
    - Get user decision về preferred libraries

5. **Implementation Plan Proposal**

    - Phác thảo implementation plan đơn giản và rõ ràng
    - Break down into smaller, manageable steps
    - Estimate complexity và potential challenges
    - Present plan for user approval

6. **User Approval Gate**
    - Chờ explicit user approval trước khi coding
    - Address any concerns hoặc modifications
    - Confirm final approach và proceed authorization

## Post-Implementation Testing Requirements

### Testing Checklist (Required after each implementation)

1. **Functionality Integration**

    - Integrate module vào `src/main.rs` để demonstrate functionality (nếu phù hợp)
    - Hoặc tạo dedicated test file trong thư mục `tests/` để verify correctness
    - Ensure module chạy độc lập theo design principles

2. **Core Functionality Validation**

    - Test phải validate core functionality đầy đủ
    - Cover edge cases và error scenarios
    - Verify performance expectations (nếu applicable)

3. **Configuration Requirements**

    - Nếu module yêu cầu configuration để hoạt động:
    - Yêu cầu user cung cấp các config values cần thiết
    - Document clearly các config parameters required
    - Provide example configuration format
    - Test với actual config values từ user

4. **Module Completion Criteria**

    - Chỉ consider module "complete" khi:
    - Tests pass successfully
    - Functionality verified và working as expected
    - User confirms satisfaction với implementation
    - Documentation updated (nếu cần)

5. **Integration Readiness**
    - Module ready for integration với other components
    - Dependencies clearly documented
    - Interface contracts well-defined

## Roadmap phát triển theo thứ tự ưu tiên

### Phase 1: Core Foundation (Priority: CRITICAL)

**Mục tiêu**: Thiết lập core modules độc lập để làm foundation cho toàn bộ project

#### Task 1.1: Core Configuration System ✅ **HOÀN THÀNH**

-   **✅ Completed Implementation**:

    -   **Files**:
        -   `src/config/app.rs` - AppConfig struct với validation
        -   `src/config/logger.rs` - Logger config schema
        -   `src/core/config.rs` - Configuration loading logic
        -   `src/config/mod.rs` và `src/core/mod.rs` - Module declarations
    -   **Dependencies**: config v0.15.11, validator v0.20.0, serde v1.0.219, anyhow v1.0.98
    -   **Features**:
        -   TOML và JSON config file support
        -   Environment variable override với nested structure
        -   Zod-like validation với custom validators
        -   Default values cho optional fields
        -   Comprehensive error handling

-   **✅ Completed Testing**:
    -   Default configuration loading
    -   TOML/JSON file loading
    -   Environment variable override
    -   Validation rejection của invalid values
    -   Error handling và fallback behavior

#### Task 1.2: Core Logging System ✅ **HOÀN THÀNH**

-   **✅ Completed Implementation**:

    -   **Files**:
        -   `src/config/logger.rs` - Enhanced logger configuration schema
        -   `src/core/logger.rs` - Core logging implementation với tracing ecosystem
        -   `src/core/mod.rs` - Updated module declarations
    -   **Dependencies**: tracing v0.1.41, tracing-subscriber v0.3.19, tracing-appender v0.2.3
    -   **Features**:
        -   Dual format support (JSON/Pretty) với configurable options
        -   Multiple log levels (trace, debug, info, warn, error, off) với validation
        -   Module-specific filtering với RUST_LOG environment variable support
        -   Async non-blocking logging với 3.4x performance improvement
        -   Rich error context với automatic file/line/module capture
        -   Dynamic project name detection với env!("CARGO_PKG_NAME")
        -   Code duplication elimination through macro refactoring

-   **✅ Completed Testing**:
    -   Default configuration loading và logger initialization
    -   JSON và Pretty format output verification
    -   Multiple log levels functionality (trace, debug, info, warn, error)
    -   Module-specific filtering với RUST_LOG environment variable
    -   Structured logging với custom fields
    -   Async vs sync performance comparison (4.8ms vs 16.4ms cho 1000 logs)
    -   Environment variable override testing

#### Task 1.3: Core Module Declaration ✅ **HOÀN THÀNH**

-   **✅ Completed**: Config và logger modules đã được declared trong `src/core/mod.rs`
-   **✅ Verified**: Both modules pass tests và user confirmed satisfaction
-   **✅ Ready**: Core foundation hoàn thành, ready cho Phase 2

### Phase 2: GRPC Client Implementation (Priority: HIGH)

**Mục tiêu**: Implement independent GRPC client để connect với Jito endpoints

#### Task 2.1: GRPC Dependencies Setup

-   **Dependency Consultation**: Hỏi user chọn thư viện cho GRPC implementation
-   **Suggested Options**: `tonic + prost`, `grpcio`, hoặc alternatives
-   **Action**: Cài đặt dependencies và setup build.rs cho protobuf generation
-   **Output**: Generated protobuf structs và service clients từ shredstream.proto

#### Task 2.2: Basic GRPC Client

-   **File**: `src/common/client.rs`
-   **Action**: Implement basic GRPC client chỉ để connect và receive raw entries
-   **Single Responsibility**: Chỉ làm connection management và return raw data
-   **Features**: Basic connection, error handling, reconnection logic
-   **Independence**: Module này chạy độc lập, chỉ depend vào core modules

#### Task 2.3: Endpoint Configuration

-   **File**: `src/config/endpoints.rs`
-   **Action**: Configuration cho GRPC endpoints (chỉ basic endpoint list)
-   **Features**: Endpoint URLs, connection timeouts
-   **Timing**: Implement sau khi Task 2.2 hoàn thành và cần configuration

#### Task 2.4: Subscription Implementation

-   **Action**: Implement SubscribeEntriesRequest với basic filtering
-   **Features**: Account filtering, basic subscription logic
-   **Independence**: Chỉ focus vào subscription mechanism, không xử lý data

#### Task 2.5: Common Module Declaration

-   **File**: `src/common/mod.rs`
-   **Action**: Declare client module sau khi implement xong
-   **Timing**: Chỉ thực hiện sau khi Task 2.2 hoàn thành

### Phase 3: Entry Processing Engine (Priority: HIGH)

**Mục tiêu**: Implement independent processing modules cho Entry data

#### Task 3.1: Serialization Dependencies

-   **Dependency Consultation**: Hỏi user chọn thư viện cho serialization
-   **Suggested Options**: `bincode`, `serde_json`, `postcard`, hoặc custom implementation
-   **Action**: Cài đặt dependencies cần thiết cho deserialization

#### Task 3.2: Entry Deserialization

-   **File**: `src/common/processor.rs`
-   **Action**: Implement bincode deserialization cho Entry objects
-   **Single Responsibility**: Chỉ deserialize, không làm gì khác
-   **Features**: Error handling, validation, performance optimization
-   **Independence**: Module này chạy độc lập, chỉ depend vào core modules

#### Task 3.3: Transaction Extraction

-   **Action**: Extract transactions từ Entry.entries field
-   **Features**: Batch processing, memory efficiency, error recovery
-   **Independence**: Separate function, không mix với deserialization logic

#### Task 3.4: Deduplication Engine

-   **Action**: Implement sliding window deduplication
-   **Features**: Hash-based deduplication, memory-bounded window, performance metrics
-   **Independence**: Standalone module, có thể test độc lập

#### Task 3.5: Transaction Filtering

-   **File**: `src/config/filters.rs`
-   **Action**: Configuration cho filtering rules (chỉ basic account filtering)
-   **Features**: Account-based filters, configurable rules
-   **Timing**: Implement khi cần filtering functionality

### Phase 4: WebSocket Server (Priority: HIGH)

**Mục tiêu**: Implement independent WebSocket server cho data broadcasting

#### Task 4.1: WebSocket Dependencies

-   **Dependency Consultation**: Hỏi user chọn thư viện cho WebSocket server
-   **Suggested Options**: `tokio-tungstenite`, `axum với WebSocket`, `warp`, hoặc alternatives
-   **Action**: Cài đặt dependencies cần thiết cho WebSocket functionality

#### Task 4.2: Basic WebSocket Server

-   **File**: `src/common/server.rs`
-   **Action**: Implement basic WebSocket server chỉ để accept connections
-   **Single Responsibility**: Chỉ làm connection management, không xử lý business logic
-   **Features**: Accept connections, basic error handling, connection cleanup
-   **Independence**: Module này chạy độc lập, chỉ depend vào core modules

#### Task 4.3: Broadcasting Logic

-   **File**: `src/common/broadcaster.rs`
-   **Action**: Implement data broadcasting mechanism
-   **Single Responsibility**: Chỉ làm message broadcasting, không filter data
-   **Features**: Send messages to connected clients, handle disconnections
-   **Independence**: Separate từ server logic, có thể test độc lập

#### Task 4.4: Server Configuration

-   **File**: `src/config/server.rs`
-   **Action**: Configuration cho WebSocket server (chỉ basic settings)
-   **Features**: Binding address, port, basic connection settings
-   **Timing**: Implement khi cần configuration cho server

### Phase 5: Main Application Integration (Priority: HIGH)

**Mục tiêu**: Integrate tất cả modules thành complete application

#### Task 5.1: Main Application Logic

-   **File**: `src/main.rs`
-   **Action**: Implement main application orchestration
-   **Features**: Initialize modules, coordinate data flow, graceful shutdown
-   **Integration**: Connect GRPC client → processor → WebSocket server

#### Task 5.2: Configuration Integration

-   **Action**: Update core config để support tất cả modules đã implement
-   **Features**: Unified configuration file, validation, environment overrides
-   **Timing**: Sau khi tất cả modules đã có basic implementation

#### Task 5.3: Error Handling Integration

-   **Action**: Implement comprehensive error handling across modules
-   **Features**: Structured errors, recovery strategies, logging integration
-   **Independence**: Có thể implement incrementally cho từng module

### Phase 6: Monitoring và Performance (Priority: MEDIUM)

**Mục tiêu**: Production-ready monitoring và optimization

#### Task 6.1: Monitoring Dependencies

-   **Dependency Consultation**: Hỏi user chọn thư viện cho monitoring
-   **Suggested Options**: `metrics`, `prometheus`, `tracing-metrics`, hoặc custom
-   **Action**: Cài đặt dependencies cho monitoring system

#### Task 6.2: Performance Monitoring

-   **File**: `src/common/monitor.rs`
-   **Action**: Implement performance metrics collection
-   **Features**: Latency tracking, throughput metrics, resource usage
-   **Independence**: Module này chạy độc lập, có thể disable được

#### Task 6.3: Health Checks

-   **Action**: Implement comprehensive health check system
-   **Features**: Module health status, processing pipeline monitoring
-   **Independence**: Separate endpoint, không impact main processing

### Phase 7: Utilities và Optimization (Priority: LOW)

**Mục tiêu**: Reusable utilities và performance optimization

#### Task 7.1: Utility Functions

-   **File**: `src/utils/mod.rs` + additional files
-   **Action**: Extract reusable functions từ các modules đã implement
-   **Categories**: Network utils, data processing utils, time utils
-   **Timing**: Sau khi có enough code để extract utilities

#### Task 7.2: Performance Optimization

-   **Action**: Profile và optimize critical paths
-   **Focus**: Memory allocation, CPU usage, network efficiency
-   **Independence**: Có thể optimize từng module độc lập

#### Task 7.3: Utils Module Declaration

-   **File**: `src/utils/mod.rs`
-   **Action**: Declare utility modules sau khi implement xong
-   **Timing**: Chỉ thực hiện sau khi có utilities để declare

### Phase 8: Integration Testing và Production (Priority: MEDIUM)

**Mục tiêu**: End-to-end testing và production readiness

#### Task 8.1: Configuration Files

-   **Action**: Create example configuration files
-   **Files**: `config.yaml`, `config.toml` examples
-   **Features**: Complete configuration với tất cả modules

#### Task 8.2: Integration Testing

-   **Action**: Test với real Jito endpoints
-   **Validation**: Data accuracy, performance benchmarks, stability testing
-   **Independence**: Test từng module trước khi test end-to-end

#### Task 8.3: Production Optimization

-   **Action**: Final optimization cho production deployment
-   **Focus**: Memory usage, CPU efficiency, error recovery
-   **Features**: Graceful shutdown, signal handling, resource cleanup

## Dependencies giữa các task

### Core Dependencies (Minimal)

1. **Task 1.1** (Core Config) → Task 1.2 (Core Logger)
2. **Task 1.3** (Core Module Declaration) → Sau khi Task 1.1, 1.2 hoàn thành
3. **Task 2.1** (GRPC Dependencies) → Task 2.2 (Basic GRPC Client)
4. **Task 3.1** (Serialization Dependencies) → Task 3.2 (Entry Deserialization)
5. **Task 4.1** (WebSocket Dependencies) → Task 4.2 (Basic WebSocket Server)

### Module Independence Strategy

-   **Phase 2, 3, 4** có thể develop hoàn toàn song song sau Phase 1
-   Mỗi module chỉ depend vào core modules (config, logger)
-   Không có cross-dependencies giữa business logic modules
-   Integration chỉ xảy ra ở Phase 5 (Main Application)

### Dependency Consultation Points

-   **Before Task 1.1**: Hỏi user về config library choice
-   **Before Task 1.2**: Hỏi user về logging library choice
-   **Before Task 2.1**: Hỏi user về GRPC library choice
-   **Before Task 3.1**: Hỏi user về serialization library choice
-   **Before Task 4.1**: Hỏi user về WebSocket library choice
-   **Before Task 6.1**: Hỏi user về monitoring library choice

## Milestone và checkpoint quan trọng

### Milestone 1: Core Foundation (End of Phase 1)

-   **Criteria**: Core config và logger modules hoàn thành và chạy độc lập
-   **Deliverable**: Basic configuration loading và structured logging
-   **Timeline**: 1-2 development sessions
-   **Validation**: Config loads successfully, logger outputs structured logs

### Milestone 2: Independent Modules (End of Phase 2, 3, 4)

-   **Criteria**: Mỗi business logic module chạy độc lập được
-   **Deliverable**: GRPC client, Entry processor, WebSocket server (separate)
-   **Timeline**: 2-3 development sessions per module
-   **Validation**: Từng module test được độc lập

### Milestone 3: Application Integration (End of Phase 5)

-   **Criteria**: Tất cả modules integrate thành complete application
-   **Deliverable**: End-to-end data flow từ Jito đến WebSocket clients
-   **Timeline**: 2-3 development sessions
-   **Validation**: Complete proxy functionality working

### Milestone 4: Production Ready (End of Phase 6)

-   **Criteria**: Monitoring và performance optimization hoàn thành
-   **Deliverable**: Production-ready application với monitoring
-   **Timeline**: 2-3 development sessions
-   **Validation**: Performance metrics, health checks, stability testing

### Milestone 5: Optimized và Tested (End of Phase 7, 8)

-   **Criteria**: Utilities extracted, performance optimized, fully tested
-   **Deliverable**: Optimized application với comprehensive testing
-   **Timeline**: 3-4 development sessions
-   **Validation**: Performance benchmarks, integration tests pass

## Tiêu chí hoàn thành cho từng giai đoạn

### Phase 1 Completion Criteria ✅ **HOÀN THÀNH**

-   [x] Core config module loads configuration successfully ✅
-   [x] Configuration supports TOML/JSON formats ✅
-   [x] Environment variable override works ✅
-   [x] Validation system functional ✅
-   [x] Core module declarations updated in mod.rs ✅
-   [x] Core logger module outputs structured logs ✅
-   [x] Logger integrates với config system ✅
-   [x] Async non-blocking logging implemented ✅
-   [x] Module-specific filtering functional ✅
-   [x] Performance optimization completed ✅

### Phase 2 Completion Criteria

-   [ ] GRPC client connects to Jito endpoints independently
-   [ ] Subscription requests sent successfully
-   [ ] Raw Entry data received và logged
-   [ ] Module chạy độc lập, chỉ depend vào core modules

### Phase 3 Completion Criteria

-   [ ] Entry data deserializes correctly independently
-   [ ] Transactions extracted from entries successfully
-   [ ] Deduplication engine works as standalone module
-   [ ] Each processing step có thể test độc lập

### Phase 4 Completion Criteria

-   [ ] WebSocket server accepts connections independently
-   [ ] Broadcasting mechanism works as separate module
-   [ ] Multiple clients supported simultaneously
-   [ ] Server chạy độc lập, chỉ depend vào core modules

### Phase 5 Completion Criteria

-   [ ] All modules integrate successfully in main.rs
-   [ ] End-to-end data flow works correctly
-   [ ] Configuration supports all implemented modules
-   [ ] Error handling works across module boundaries

### Phase 6 Completion Criteria

-   [ ] Performance metrics collected và exposed
-   [ ] Health checks report system status
-   [ ] Monitoring works independently
-   [ ] Production-level stability achieved

## Next Steps

### Immediate Action Required

**✅ Phase 1: Core Foundation COMPLETED**

**All Phase 1 tasks hoàn thành thành công:**

-   ✅ Task 1.1: Core Configuration System
-   ✅ Task 1.2: Core Logging System
-   ✅ Task 1.3: Core Module Declaration

**🚀 Ready to start Phase 2, 3, hoặc 4 (có thể develop song song):**

**Phase 2: GRPC Client Implementation**

-   Task 2.1: GRPC Dependencies Setup (cần consultation)
-   Task 2.2: Basic GRPC Client
-   Task 2.3: Endpoint Configuration
-   Task 2.4: Subscription Implementation
-   Task 2.5: Common Module Declaration

**Phase 3: Entry Processing Engine**

-   Task 3.1: Serialization Dependencies (cần consultation)
-   Task 3.2: Entry Deserialization
-   Task 3.3: Transaction Extraction
-   Task 3.4: Deduplication Engine
-   Task 3.5: Transaction Filtering

**Phase 4: WebSocket Server**

-   Task 4.1: WebSocket Dependencies (cần consultation)
-   Task 4.2: Basic WebSocket Server
-   Task 4.3: Broadcasting Logic
-   Task 4.4: Server Configuration

### Development Process Adherence

1. **Consultation First**: Complete consultation process trước mỗi module
2. **User Approval Gates**: Wait for explicit approval ở consultation và testing phases
3. **Testing Mandatory**: Every module must be tested và verified
4. **Module Declaration**: Update mod.rs chỉ sau khi testing complete
5. **Quality Assurance**: No shortcuts, complete cycle cho mỗi module
6. **Documentation**: Update CONTEXT.md khi có significant changes

## Development Workflow

### Complete Module Development Cycle

1. **Pre-Implementation Consultation Phase**

    - Follow consultation checklist completely
    - Clarify purpose, requirements, và features
    - Discuss implementation strategy với user
    - Present library options với rationale
    - Propose detailed implementation plan
    - **WAIT for explicit user approval trước khi coding**

2. **Implementation Phase**

    - Focus vào single responsibility principle
    - Implement theo approved plan
    - Maintain module independence
    - Follow zero comments policy
    - Code must be self-documenting

3. **Testing Phase**

    - Integrate vào main.rs để demonstrate (nếu phù hợp)
    - Hoặc create dedicated test file trong `tests/` directory
    - Test core functionality và edge cases
    - Request user config values nếu module requires configuration
    - Document config parameters clearly
    - **WAIT for user confirmation về test results**

4. **Module Declaration Phase**

    - Update mod.rs chỉ khi module hoàn thành và tested
    - Ensure module ready for integration
    - Document dependencies và interface contracts

5. **Integration Readiness**
    - Module considered complete chỉ khi:
    - Tests pass successfully
    - User confirms satisfaction
    - Documentation updated
    - Ready for integration với other modules

### Quality Assurance

-   **No Shortcuts**: Mỗi module phải go through complete cycle
-   **User Approval Gates**: Required ở consultation và testing phases
-   **Independence Validation**: Module phải chạy độc lập được
-   **Integration Preparation**: Chỉ integrate ở Phase 5 khi tất cả modules ready
