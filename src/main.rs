mod config;
mod core;

use core::{config::load_config, logger::Logger};

fn main() -> anyhow::Result<()> {
    println!("=== Shreder Core Logging System Test ===\n");

    let config = load_config()?;
    println!("✅ Configuration loaded successfully");

    let _logger = Logger::init(&config.logger)?;
    println!("✅ Logger initialized successfully\n");

    println!("=== Testing Different Log Levels ===");
    tracing::trace!("This is a TRACE message");
    tracing::debug!("This is a DEBUG message");
    tracing::info!("This is an INFO message");
    tracing::warn!("This is a WARN message");
    tracing::error!("This is an ERROR message");

    println!("\n=== Testing Structured Logging ===");
    tracing::info!(module = "main", action = "testing", count = 42, "Structured logging with fields");

    println!("\n=== Testing Module-specific Logging ===");
    tracing::info!(target: "shreder::core", "Core module log");
    tracing::debug!(target: "shreder::processor", "Processor debug log");
    tracing::warn!(target: "shreder::client", "Client warning log");

    println!("\n=== Performance Test ===");
    let start = std::time::Instant::now();
    for i in 0..1000 {
        tracing::debug!(iteration = i, "Performance test iteration");
    }
    let duration = start.elapsed();
    tracing::info!(duration_ns = duration.as_nanos(), iterations = 1000, "Performance test completed");

    println!("\n✅ All logging tests completed successfully!");
    println!("💡 Try different log levels with: RUST_LOG=debug cargo run");
    println!("💡 Try JSON format by updating config file");

    Ok(())
}
