use std::io;

use tracing_appender::non_blocking::WorkerGuard;
use tracing_subscriber::{EnvFilter, fmt, prelude::*};

use crate::config::logger::{LogFormat, LoggerConfig};

pub struct Logger {
    _guard: Option<WorkerGuard>,
}

macro_rules! init_subscriber {
    ($config:expr, $env_filter:expr, $writer:expr) => {
        let base_layer = fmt::layer()
            .with_writer($writer)
            .with_target($config.show_target)
            .with_thread_ids($config.show_thread_ids);

        let formatted_layer = match $config.format {
            LogFormat::Json => base_layer.json().boxed(),
            LogFormat::Pretty => {
                if $config.compact_format {
                    base_layer.compact().boxed()
                } else {
                    base_layer.pretty().boxed()
                }
            }
        };

        tracing_subscriber::registry()
            .with($env_filter)
            .with(formatted_layer)
            .init();
    };
}

impl Logger {
    pub fn init(config: &LoggerConfig) -> anyhow::Result<Self> {
        let env_filter = build_env_filter(config)?;

        if config.async_logging {
            let (non_blocking, guard) = tracing_appender::non_blocking(io::stdout());
            init_subscriber!(config, env_filter, non_blocking);
            Ok(Self { _guard: Some(guard) })
        } else {
            init_subscriber!(config, env_filter, io::stdout);
            Ok(Self { _guard: None })
        }
    }
}

fn build_env_filter(config: &LoggerConfig) -> anyhow::Result<EnvFilter> {
    let filter = if let Some(ref module_filters) = config.module_filters {
        EnvFilter::try_new(module_filters).map_err(|e| anyhow::anyhow!("Invalid module filters: {}", e))?
    } else if let Ok(env_filter) = std::env::var("RUST_LOG") {
        EnvFilter::try_new(&env_filter).map_err(|e| anyhow::anyhow!("Invalid RUST_LOG: {}", e))?
    } else {
        EnvFilter::new(format!("{}={}", env!("CARGO_PKG_NAME"), config.level.to_lowercase()))
    };

    Ok(filter)
}
